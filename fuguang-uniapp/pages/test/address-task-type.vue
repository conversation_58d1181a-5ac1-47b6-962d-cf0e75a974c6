<template>
  <view class="test-container">
    <view class="test-section">
      <text class="section-title">地址选择测试</text>
      <view class="test-item">
        <text class="label">任务地址：</text>
        <AddressSelector v-model="testAddress" placeholder="请选择任务地址" @change="onAddressChange" />
      </view>
      <view class="result" v-if="addressResult">
        <text class="result-title">选择结果：</text>
        <text class="result-text">地址：{{ addressResult.address }}</text>
        <text class="result-text">经度：{{ addressResult.longitude }}</text>
        <text class="result-text">纬度：{{ addressResult.latitude }}</text>
      </view>

      <view class="test-buttons">
        <u-button type="primary" size="small" @click="testAddressDisplay">测试地址显示</u-button>
        <u-button type="warning" size="small" @click="clearAddress">清空地址</u-button>
      </view>
    </view>

    <view class="test-section">
      <text class="section-title">任务类型测试</text>
      <view class="test-item">
        <text class="label">一级类型：</text>
        <view class="type-input" @click="showFirstTypePicker = true">
          <text class="type-text" :class="{ 'placeholder': !selectedFirstTypeName }">
            {{ selectedFirstTypeName || '请选择一级类型' }}
          </text>
          <u-icon name="arrow-right" size="24" color="#999"></u-icon>
        </view>
      </view>

      <view class="test-item" v-if="firstTypeId">
        <text class="label">二级类型：</text>
        <view class="type-input" @click="showSecondTypePicker = true">
          <text class="type-text" :class="{ 'placeholder': !selectedSecondTypeName }">
            {{ selectedSecondTypeName || '请选择二级类型' }}
          </text>
          <u-icon name="arrow-right" size="24" color="#999"></u-icon>
        </view>
      </view>

      <view class="result" v-if="firstTypeId">
        <text class="result-title">选择结果：</text>
        <text class="result-text">一级类型ID：{{ firstTypeId }}</text>
        <text class="result-text">一级类型名称：{{ selectedFirstTypeName }}</text>
        <text class="result-text" v-if="secondTypeId">二级类型ID：{{ secondTypeId }}</text>
        <text class="result-text" v-if="selectedSecondTypeName">二级类型名称：{{ selectedSecondTypeName }}</text>
      </view>
    </view>

    <!-- 一级类型选择器 -->
    <u-picker :show="showFirstTypePicker" :columns="firstTypeColumns" @confirm="onFirstTypeConfirm"
      @cancel="showFirstTypePicker = false"></u-picker>

    <!-- 二级类型选择器 -->
    <u-picker :show="showSecondTypePicker" :columns="secondTypeColumns" @confirm="onSecondTypeConfirm"
      @cancel="showSecondTypePicker = false"></u-picker>

    <view class="debug-section">
      <text class="section-title">调试信息</text>
      <view class="debug-info">
        <text class="debug-text">一级类型数量：{{ firstTypes.length }}</text>
        <text class="debug-text">二级类型数量：{{ secondTypes.length }}</text>
        <text class="debug-text">地址选择状态：{{ testAddress ? '已选择' : '未选择' }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import { getFirstLevelTaskTypes, getChildrenTaskTypes } from '@/api/taskType'
import AddressSelector from '@/components/AddressSelector/AddressSelector.vue'

export default {
  components: {
    AddressSelector
  },
  data() {
    return {
      // 地址相关
      testAddress: '',
      addressResult: null,

      // 任务类型相关
      firstTypes: [],
      secondTypes: [],
      firstTypeId: '',
      secondTypeId: '',
      selectedFirstTypeName: '',
      selectedSecondTypeName: '',
      showFirstTypePicker: false,
      showSecondTypePicker: false
    }
  },

  computed: {
    firstTypeColumns() {
      return [this.firstTypes.map(item => ({ text: item.typeName, value: item.typeId }))]
    },

    secondTypeColumns() {
      return [this.secondTypes.map(item => ({ text: item.typeName, value: item.typeId }))]
    }
  },

  onLoad() {
    this.loadTaskTypes()
  },

  methods: {
    // 地址变更处理
    onAddressChange(data) {
      console.log('地址选择结果:', data)
      this.addressResult = data

      // 强制更新显示
      this.$forceUpdate()
    },

    // 加载任务类型数据
    async loadTaskTypes() {
      try {
        console.log('开始加载一级任务类型')
        const res = await getFirstLevelTaskTypes()
        console.log('一级任务类型加载结果:', res)
        this.firstTypes = res.data || []
      } catch (error) {
        console.error('加载任务类型失败:', error)
        uni.showToast({
          title: '加载任务类型失败',
          icon: 'none'
        })
      }
    },

    // 一级类型选择确认
    async onFirstTypeConfirm(value) {
      console.log('选择一级类型:', value)
      const selectedType = this.firstTypes.find(item => item.typeId === value[0])
      if (selectedType) {
        this.firstTypeId = selectedType.typeId
        this.selectedFirstTypeName = selectedType.typeName

        // 重置二级类型
        this.secondTypeId = ''
        this.selectedSecondTypeName = ''
        this.secondTypes = []

        // 加载二级类型
        try {
          console.log('开始加载二级任务类型，父类型ID:', selectedType.typeId)
          uni.showLoading({ title: '加载中...' })
          const res = await getChildrenTaskTypes(selectedType.typeId)
          console.log('二级任务类型加载结果:', res)
          this.secondTypes = res.data || []

          if (this.secondTypes.length === 0) {
            uni.showToast({
              title: '该类型暂无子类型',
              icon: 'none'
            })
          }
        } catch (error) {
          console.error('加载二级类型失败:', error)
          uni.showToast({
            title: '加载二级类型失败',
            icon: 'none'
          })
        } finally {
          uni.hideLoading()
        }
      }
      this.showFirstTypePicker = false
    },

    // 二级类型选择确认
    onSecondTypeConfirm(value) {
      console.log('选择二级类型:', value)
      const selectedType = this.secondTypes.find(item => item.typeId === value[0])
      if (selectedType) {
        this.secondTypeId = selectedType.typeId
        this.selectedSecondTypeName = selectedType.typeName
        console.log('已选择二级类型:', selectedType.typeName)
      }
      this.showSecondTypePicker = false
    },

    // 测试地址显示
    testAddressDisplay() {
      console.log('当前地址值:', this.testAddress)
      console.log('地址结果:', this.addressResult)
      uni.showModal({
        title: '地址信息',
        content: `地址: ${this.testAddress}\n结果: ${JSON.stringify(this.addressResult)}`,
        showCancel: false
      })
    },

    // 清空地址
    clearAddress() {
      this.testAddress = ''
      this.addressResult = null
      console.log('地址已清空')
    }
  }
}
</script>

<style lang="scss" scoped>
.test-container {
  padding: 40rpx;
  background: #f8f8f8;
  min-height: 100vh;
}

.test-section {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.test-item {
  margin-bottom: 20rpx;

  .label {
    display: block;
    font-size: 28rpx;
    color: #666;
    margin-bottom: 10rpx;
  }
}

.type-input {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  cursor: pointer;

  .type-text {
    font-size: 30rpx;
    color: #333;

    &.placeholder {
      color: #999;
    }
  }
}

.result {
  background: #f8f8f8;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-top: 20rpx;

  .result-title {
    display: block;
    font-size: 28rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 10rpx;
  }

  .result-text {
    display: block;
    font-size: 26rpx;
    color: #666;
    line-height: 1.5;
    margin-bottom: 5rpx;
  }
}

.debug-section {
  background: #fff7f0;
  border-radius: 20rpx;
  padding: 30rpx;
  border-left: 8rpx solid #ff6b35;
}

.debug-info {
  .debug-text {
    display: block;
    font-size: 26rpx;
    color: #666;
    line-height: 1.6;
    margin-bottom: 10rpx;
  }
}

.test-buttons {
  display: flex;
  gap: 20rpx;
  margin-top: 20rpx;
}
</style>
