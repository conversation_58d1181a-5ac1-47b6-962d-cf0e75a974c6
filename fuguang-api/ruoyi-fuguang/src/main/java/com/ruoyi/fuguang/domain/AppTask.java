package com.ruoyi.fuguang.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * APP任务对象 app_task
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
public class AppTask extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 任务ID */
    private Long taskId;

    /** 任务标题 */
    @Excel(name = "任务标题")
    private String taskTitle;

    /** 任务描述 */
    @Excel(name = "任务描述")
    private String taskDesc;

    /** 任务金额 */
    @Excel(name = "任务金额")
    private BigDecimal taskAmount;

    /** 任务类型（0普通任务 1紧急任务） */
    @Excel(name = "任务类型", readConverterExp = "0=普通任务,1=紧急任务")
    private String taskType;

    /** 一级任务类型ID */
    @Excel(name = "一级任务类型ID")
    private Long firstTypeId;

    /** 二级任务类型ID */
    @Excel(name = "二级任务类型ID")
    private Long secondTypeId;

    /** 紧急程度（0普通 1紧急 2非常紧急） */
    @Excel(name = "紧急程度", readConverterExp = "0=普通,1=紧急,2=非常紧急")
    private String urgentLevel;

    /** 任务状态（0待接取 1进行中 2已完成 3已取消） */
    @Excel(name = "任务状态", readConverterExp = "0=待接取,1=进行中,2=已完成,3=已取消")
    private String taskStatus;

    /** 发布者ID */
    @Excel(name = "发布者ID")
    private Long publisherId;

    /** 发布者昵称 */
    @Excel(name = "发布者昵称")
    private String publisherName;

    /** 发布者头像 */
    private String publisherAvatar;

    /** 接收者ID */
    @Excel(name = "接收者ID")
    private Long receiverId;

    /** 接收者昵称 */
    @Excel(name = "接收者昵称")
    private String receiverName;

    /** 任务地址 */
    @Excel(name = "任务地址")
    private String taskAddress;

    /** 经度 */
    private String longitude;

    /** 纬度 */
    private String latitude;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 浏览次数 */
    @Excel(name = "浏览次数")
    private Integer viewCount;

    /** 热度分数 */
    @Excel(name = "热度分数")
    private Integer hotScore;

    public void setTaskId(Long taskId) 
    {
        this.taskId = taskId;
    }

    public Long getTaskId() 
    {
        return taskId;
    }

    public void setTaskTitle(String taskTitle) 
    {
        this.taskTitle = taskTitle;
    }

    public String getTaskTitle() 
    {
        return taskTitle;
    }

    public void setTaskDesc(String taskDesc) 
    {
        this.taskDesc = taskDesc;
    }

    public String getTaskDesc() 
    {
        return taskDesc;
    }

    public void setTaskAmount(BigDecimal taskAmount) 
    {
        this.taskAmount = taskAmount;
    }

    public BigDecimal getTaskAmount() 
    {
        return taskAmount;
    }

    public void setTaskType(String taskType) 
    {
        this.taskType = taskType;
    }

    public String getTaskType() 
    {
        return taskType;
    }

    public void setTaskStatus(String taskStatus) 
    {
        this.taskStatus = taskStatus;
    }

    public String getTaskStatus() 
    {
        return taskStatus;
    }

    public void setPublisherId(Long publisherId) 
    {
        this.publisherId = publisherId;
    }

    public Long getPublisherId() 
    {
        return publisherId;
    }

    public void setPublisherName(String publisherName) 
    {
        this.publisherName = publisherName;
    }

    public String getPublisherName() 
    {
        return publisherName;
    }

    public void setPublisherAvatar(String publisherAvatar) 
    {
        this.publisherAvatar = publisherAvatar;
    }

    public String getPublisherAvatar() 
    {
        return publisherAvatar;
    }

    public void setReceiverId(Long receiverId) 
    {
        this.receiverId = receiverId;
    }

    public Long getReceiverId() 
    {
        return receiverId;
    }

    public void setReceiverName(String receiverName) 
    {
        this.receiverName = receiverName;
    }

    public String getReceiverName() 
    {
        return receiverName;
    }

    public void setTaskAddress(String taskAddress) 
    {
        this.taskAddress = taskAddress;
    }

    public String getTaskAddress() 
    {
        return taskAddress;
    }

    public void setLongitude(String longitude) 
    {
        this.longitude = longitude;
    }

    public String getLongitude() 
    {
        return longitude;
    }

    public void setLatitude(String latitude) 
    {
        this.latitude = latitude;
    }

    public String getLatitude() 
    {
        return latitude;
    }

    public void setStartTime(Date startTime) 
    {
        this.startTime = startTime;
    }

    public Date getStartTime() 
    {
        return startTime;
    }

    public void setEndTime(Date endTime) 
    {
        this.endTime = endTime;
    }

    public Date getEndTime() 
    {
        return endTime;
    }

    public void setViewCount(Integer viewCount) 
    {
        this.viewCount = viewCount;
    }

    public Integer getViewCount() 
    {
        return viewCount;
    }

    public void setHotScore(Integer hotScore) 
    {
        this.hotScore = hotScore;
    }

    public Integer getHotScore()
    {
        return hotScore;
    }

    public void setFirstTypeId(Long firstTypeId)
    {
        this.firstTypeId = firstTypeId;
    }

    public Long getFirstTypeId()
    {
        return firstTypeId;
    }

    public void setSecondTypeId(Long secondTypeId)
    {
        this.secondTypeId = secondTypeId;
    }

    public Long getSecondTypeId()
    {
        return secondTypeId;
    }

    public void setUrgentLevel(String urgentLevel)
    {
        this.urgentLevel = urgentLevel;
    }

    public String getUrgentLevel()
    {
        return urgentLevel;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("taskId", getTaskId())
            .append("taskTitle", getTaskTitle())
            .append("taskDesc", getTaskDesc())
            .append("taskAmount", getTaskAmount())
            .append("taskType", getTaskType())
            .append("firstTypeId", getFirstTypeId())
            .append("secondTypeId", getSecondTypeId())
            .append("urgentLevel", getUrgentLevel())
            .append("taskStatus", getTaskStatus())
            .append("publisherId", getPublisherId())
            .append("publisherName", getPublisherName())
            .append("publisherAvatar", getPublisherAvatar())
            .append("receiverId", getReceiverId())
            .append("receiverName", getReceiverName())
            .append("taskAddress", getTaskAddress())
            .append("longitude", getLongitude())
            .append("latitude", getLatitude())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("viewCount", getViewCount())
            .append("hotScore", getHotScore())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
